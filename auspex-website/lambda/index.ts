import { DynamoDBClient } from "@aws-sdk/client-dynamodb";
import { DynamoDBDocumentClient, PutCommand, ScanCommand } from "@aws-sdk/lib-dynamodb";
import { S3Client, GetObjectCommand } from "@aws-sdk/client-s3";
import { getSignedUrl } from "@aws-sdk/s3-request-presigner";
import { APIGatewayProxyEvent, APIGatewayProxyResult } from "aws-lambda";

const client = new DynamoDBClient({});
const dynamodb = DynamoDBDocumentClient.from(client);
const s3Client = new S3Client({});
const RELEASES_TABLE = process.env.RELEASES_TABLE!;
const PERFORMANCES_TABLE = process.env.PERFORMANCES_TABLE!;
const RELEASES_BUCKET = process.env.RELEASES_BUCKET || "auspex-records-release";

export const handler = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
  try {
    const { httpMethod, resource } = event;

    // CORS headers
    const headers = {
      "Access-Control-Allow-Origin": "*",
      "Access-Control-Allow-Headers": "Content-Type",
      "Access-Control-Allow-Methods": "OPTIONS,GET,POST",
    };

    // Handle releases
    if (resource === "/releases") {
      if (httpMethod === "GET") {
        const result = await dynamodb.send(
          new ScanCommand({
            TableName: RELEASES_TABLE,
          }),
        );

        return {
          statusCode: 200,
          headers,
          body: JSON.stringify(result.Items),
        };
      }

      if (httpMethod === "POST") {
        const release = JSON.parse(event.body || "{}");
        await dynamodb.send(
          new PutCommand({
            TableName: RELEASES_TABLE,
            Item: release,
          }),
        );

        return {
          statusCode: 201,
          headers,
          body: JSON.stringify(release),
        };
      }
    }

    // Handle performances
    if (resource === "/performances") {
      if (httpMethod === "GET") {
        const result = await dynamodb.send(
          new ScanCommand({
            TableName: PERFORMANCES_TABLE,
          }),
        );

        return {
          statusCode: 200,
          headers,
          body: JSON.stringify(result.Items),
        };
      }

      if (httpMethod === "POST") {
        const performance = JSON.parse(event.body || "{}");
        await dynamodb.send(
          new PutCommand({
            TableName: PERFORMANCES_TABLE,
            Item: performance,
          }),
        );

        return {
          statusCode: 201,
          headers,
          body: JSON.stringify(performance),
        };
      }
    }

    // Handle download requests
    if (resource === "/download" && httpMethod === "POST") {
      const { releaseId, format } = JSON.parse(event.body || "{}");

      if (!releaseId || !format) {
        return {
          statusCode: 400,
          headers,
          body: JSON.stringify({ message: "Missing releaseId or format" }),
        };
      }

      try {
        // Get release data from DynamoDB to get artist and title
        const releaseResult = await dynamodb.send(
          new ScanCommand({
            TableName: RELEASES_TABLE,
            FilterExpression: "id = :id",
            ExpressionAttributeValues: {
              ":id": releaseId,
            },
          }),
        );

        if (!releaseResult.Items || releaseResult.Items.length === 0) {
          return {
            statusCode: 404,
            headers,
            body: JSON.stringify({ message: "Release not found" }),
          };
        }

        const release = releaseResult.Items[0];
        const folderName = `${release.artist} - ${release.title}`;
        const fileName = `${folderName} - ${format}.zip`;
        const s3Key = `${folderName}/${fileName}`;

        // Generate presigned URL for S3 download
        const command = new GetObjectCommand({
          Bucket: RELEASES_BUCKET,
          Key: s3Key,
        });

        const downloadUrl = await getSignedUrl(s3Client, command, { expiresIn: 3600 }); // 1 hour expiry

        return {
          statusCode: 200,
          headers,
          body: JSON.stringify({ downloadUrl }),
        };
      } catch (error) {
        console.error("Download error:", error);
        return {
          statusCode: 500,
          headers,
          body: JSON.stringify({ message: "Failed to generate download link" }),
        };
      }
    }

    // Handle OPTIONS requests (CORS preflight)
    if (httpMethod === "OPTIONS") {
      return {
        statusCode: 200,
        headers,
        body: "",
      };
    }

    return {
      statusCode: 404,
      headers,
      body: JSON.stringify({ message: "Not Found" }),
    };
  } catch (error) {
    console.error("Error:", error);
    return {
      statusCode: 500,
      headers: {
        "Access-Control-Allow-Origin": "*",
      },
      body: JSON.stringify({ message: "Internal Server Error" }),
    };
  }
};
