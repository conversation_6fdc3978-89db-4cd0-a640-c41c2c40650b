
"use client";

import Link from "next/link";
import { usePathname } from "next/navigation";
import { cn } from "@/lib/utils";
import { motion } from "framer-motion";

export default function Header() {
  const pathname = usePathname();

  const navLinks = [
    { href: "/", label: "Home" },
    { href: "/releases", label: "Releases" },
    { href: "/live-sets", label: "Live Sets" },
  ];

  return (
    <motion.header 
      initial={{ y: -100, opacity: 0 }}
      animate={{ y: 0, opacity: 1 }}
      transition={{ duration: 0.5, ease: "easeOut" }}
      className="fixed top-0 left-0 right-0 z-50"
    >
      <div className="container mx-auto flex h-20 items-center justify-center px-6">
        <nav className="flex items-center gap-2 rounded-full p-2 bg-black/50 backdrop-blur-lg border border-white/20 shadow-xl">
          {navLinks.map((link) => (
            <Link
              key={link.href}
              href={link.href}
              className={cn(
                "relative text-md font-medium transition-all duration-300 px-4 py-2 rounded-full font-body",
                pathname === link.href
                  ? "text-white bg-primary shadow-lg shadow-primary/30"
                  : "text-white/80 hover:text-white hover:bg-white/10"
              )}
            >
              {link.label}
            </Link>
          ))}
        </nav>
      </div>
    </motion.header>
  );
}
