
"use client";

import Link from 'next/link';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Instagram, Youtube, Twitch } from 'lucide-react';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { AuspexLogo } from '../icons/logo';
import Header from '../layout/header';
import Footer from '../layout/footer';

const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.05,
      delayChildren: 0.1,
    },
  },
};

const itemVariants = {
  hidden: { y: 20, opacity: 0 },
  visible: {
    y: 0,
    opacity: 1,
    transition: {
      duration: 0.5,
      ease: 'easeOut',
    },
  },
};

const logoVariants = {
  hidden: { opacity: 0, scale: 0.5 },
  visible: {
    opacity: 1,
    scale: 1,
    transition: {
      duration: 0.5,
      ease: [0.22, 1, 0.36, 1],
    },
  },
};

const titleVariants = {
  hidden: { opacity: 0, y: 30 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.5,
      delay: 0.1,
      ease: 'easeOut',
    },
  },
};

const taglineLine1 = "Independent label committed to curating and cultivating";
const taglineLine2 = "future-facing sound.";
const taglineVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.02,
      delayChildren: 0.2,
    },
  },
};
const letterVariants = {
  hidden: { opacity: 0, y: 10 },
  visible: { opacity: 1, y: 0 },
};

export default function HeroCollage() {
  
  return (
    <section className="relative h-screen w-full flex flex-col items-center justify-center text-center overflow-hidden">
      <Header />
      <div className="absolute inset-0 -z-10 w-full h-full overflow-hidden">
        <div 
          className="absolute inset-0 w-full h-full bg-cover bg-center"
          style={{ backgroundImage: `url(/wallpapers/Auspex_SantaCruz.png)` }}
        />
      </div>
      <div className="absolute inset-0 bg-background/60" />

      <motion.div
        key="hero-content"
        className="relative z-10 p-4 flex flex-col items-center flex-grow justify-center"
        variants={containerVariants}
        initial="hidden"
        animate="visible"
      >
        <motion.div variants={logoVariants}>
          <AuspexLogo className="h-24 md:h-40 w-auto mb-4" />
        </motion.div>
        <motion.h1
          className="text-5xl md:text-8xl font-body text-white mb-6 tracking-wider"
          variants={titleVariants}
        >
          Auspex Records
        </motion.h1>

        <motion.div
          className="text-xl md:text-2xl max-w-3xl mx-auto text-primary mb-10 font-light"
          variants={taglineVariants}
        >
          <p className="mb-2" aria-label={taglineLine1}>
            {taglineLine1.split("").map((char, index) => (
              <motion.span
                key={`line1-${index}`}
                variants={letterVariants}
                style={{ display: 'inline-block', whiteSpace: 'pre' }}
              >
                {char}
              </motion.span>
            ))}
          </p>
           <p aria-label={taglineLine2}>
            {taglineLine2.split("").map((char, index) => (
              <motion.span
                 key={`line2-${index}`}
                variants={letterVariants}
                style={{ display: 'inline-block', whiteSpace: 'pre' }}
              >
                {char}
              </motion.span>
            ))}
          </p>
        </motion.div>
        
        <motion.div
          className="flex gap-4 justify-center"
          variants={itemVariants}
        >
          <Button asChild size="lg" variant="outline" className="font-bold text-lg bg-white/5 border-white/20 text-white hover:bg-white/10 shadow-lg transition-all duration-300 transform hover:scale-105">
            <Link href="/releases">
              Explore Releases
            </Link>
          </Button>
          <Button asChild size="lg" variant="outline" className="font-bold text-lg bg-white/5 border-white/20 text-white hover:bg-white/10 shadow-lg transition-all duration-300 transform hover:scale-105">
            <Link href="/live-sets">
              Watch Live Sets
            </Link>
          </Button>
        </motion.div>
        
        <motion.div
          className="flex flex-wrap items-center justify-center gap-8 mt-12"
          variants={itemVariants}
        >
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <a href="https://www.instagram.com/auspex_records/" target="_blank" rel="noopener noreferrer" className="text-white/70 hover:text-primary transition-colors transform hover:scale-110">
                  <Instagram className="h-7 w-7" />
                </a>
              </TooltipTrigger>
              <TooltipContent side="bottom" className="bg-black/50 border-white/10 text-white">
                <p>Instagram</p>
              </TooltipContent>
            </Tooltip>
            <Tooltip>
              <TooltipTrigger asChild>
                <a href="https://youtube.com/@AuspexRecords" target="_blank" rel="noopener noreferrer" className="text-white/70 hover:text-primary transition-colors transform hover:scale-110">
                  <Youtube className="h-7 w-7" />
                </a>
              </TooltipTrigger>
              <TooltipContent side="bottom" className="bg-black/50 border-white/10 text-white">
                <p>YouTube</p>
              </TooltipContent>
            </Tooltip>
            <Tooltip>
              <TooltipTrigger asChild>
                <a href="https://www.twitch.tv/auspexrecords" target="_blank" rel="noopener noreferrer" className="text-white/70 hover:text-primary transition-colors transform hover:scale-110">
                  <Twitch className="h-7 w-7" />
                </a>
              </TooltipTrigger>
              <TooltipContent side="bottom" className="bg-black/50 border-white/10 text-white">
                <p>Twitch</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </motion.div>
      </motion.div>
      <div className="w-full absolute bottom-0">
        <Footer />
      </div>
    </section>
  );
}
