
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 224 71.4% 4.1%;
    --foreground: 210 20% 98%;
    --card: 224 71.4% 10%;
    --card-foreground: 210 20% 98%;
    --popover: 224 71.4% 4.1%;
    --popover-foreground: 210 20% 98%;
    --primary: 24.6 95% 53.1%;
    --primary-foreground: 210 20% 98%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 20% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 217.2 32.6% 25.5%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 20% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 20% 98%;
    --border: 217.2 32.6% 27.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 24.6 95% 53.1%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 224 71.4% 4.1%;
    --foreground: 210 20% 98%;
    --card: 224 71.4% 10%;
    --card-foreground: 210 20% 98%;
    --popover: 224 71.4% 4.1%;
    --popover-foreground: 210 20% 98%;
    --primary: 24.6 95% 53.1%;
    --primary-foreground: 210 20% 98%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 20% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 217.2 32.6% 25.5%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 20% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 20% 98%;
    --border: 217.2 32.6% 27.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 24.6 95% 53.1%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground font-body;
    min-height: 100vh;
  }
}

.aurora-bg {
  opacity: 1;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: -20;
  background-image: radial-gradient(at 27% 37%, hsla(215, 98%, 62%, 0.15) 0px, transparent 50%), radial-gradient(at 97% 21%, hsla(24, 98%, 49%, 0.1) 0px, transparent 50%), radial-gradient(at 52% 99%, hsla(355, 98%, 62%, 0.1) 0px, transparent 50%), radial-gradient(at 10% 29%, hsla(256, 96%, 68%, 0.15) 0px, transparent 50%), radial-gradient(at 97% 96%, hsla(38, 60%, 74%, 0.1) 0px, transparent 50%), radial-gradient(at 33% 50%, hsla(222, 67%, 73%, 0.1) 0px, transparent 50%), radial-gradient(at 79% 53%, hsla(343, 68%, 79%, 0.1) 0px, transparent 50%);
  background-color: #040813;
  animation: aurora 60s linear infinite;
}

@layer utilities {
  .animation-delay-2000 {
    animation-delay: 2s;
  }
  .animation-delay-4000 {
    animation-delay: 4s;
  }
}

@keyframes aurora {
  from {
    background-position: 50% 50%, 50% 50%;
  }
  to {
    background-position: 350% 50%, 350% 50%;
  }
}
