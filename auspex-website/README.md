# Auspex Records - Psychedelic Trance Label Website

This is the official website for Auspex Records, an independent music label dedicated to curating and cultivating future-facing psychedelic trance music.

## Tech Stack

This project is built with a modern, high-performance web stack:

*   **[Next.js](https://nextjs.org/)**: React framework with App Router for server-side rendering and static generation
*   **[React](https://reactjs.org/)**: Component-based UI library with hooks and modern patterns
*   **[TypeScript](https://www.typescriptlang.org/)**: Type-safe JavaScript for better development experience
*   **[Tailwind CSS](https://tailwindcss.com/)**: Utility-first CSS framework for rapid styling
*   **[ShadCN UI](https://ui.shadcn.com/)**: Accessible and customizable UI component library
*   **[Framer Motion](https://www.framer.com/motion/)**: Animation library for smooth transitions and interactions
*   **[Lucide React](https://lucide.dev/)**: Modern icon library

## Architecture

*   **Frontend**: Next.js React application with static data from `src/lib/data.ts`
*   **Media Storage**: AWS S3 buckets for album downloads and cover art
*   **Deployment**: AWS CloudFront + S3 with automated CI/CD
*   **Infrastructure**: Terraform for AWS resource management

## Getting Started

To run this project locally, you'll need Node.js installed.

1.  **Install dependencies:**
    ```bash
    npm install
    ```

2.  **Run the development server:**
    ```bash
    npm run dev
    ```

The application will be available at [http://localhost:3000](http://localhost:3000).

## Features

The Auspex Records website showcases the label's activities and releases with these key features:

-   **Releases:** Browse music releases with album art, track listings, streaming platform links, and direct downloads (See `/src/app/releases/page.tsx`, `/src/components/releases/`)
-   **Live Sets:** Watch live performances and DJ sets from label artists (See `/src/app/live-sets/page.tsx`, `/src/components/sets/`)
-   **YouTube Integration:** Embedded YouTube player for track previews and live set viewing
-   **Multi-format Downloads:** Direct S3 downloads in MP3, FLAC, AAC, WAV, and other formats
-   **Year-based Filtering:** Browse releases by year with dynamic filtering
-   **Responsive Design:** Mobile-first design with smooth animations and glass morphism effects
-   **Platform Integration:** Links to Spotify, Bandcamp, SoundCloud, Apple Music, and more

## Data Structure

The website uses a static data approach for optimal performance:

-   **Release Data:** Managed in `/src/lib/data.ts` with fallback to local data
-   **Type Definitions:** Comprehensive TypeScript types in `/src/lib/types.ts`
-   **S3 Integration:** Direct downloads from `auspex-records-release` S3 bucket
-   **Media Assets:** Album covers and logos stored in `/public/` directory

## Project Structure

*   `src/app/`: Contains the main pages of the application, following the Next.js App Router structure.
*   `src/components/`: Contains reusable React components, including UI components from ShadCN (`ui/`) and custom components for the application.
*   `src/lib/`: Includes utility functions (`utils.ts`), data fetching logic (`data.ts`), and type definitions (`types.ts`).
*   `public/`: Stores static assets like images, logos, and album art.
*   `tailwind.config.ts`: Configuration file for Tailwind CSS, including custom fonts and color themes.
*   `globals.css`: Defines the global styles and CSS variables for the application's theme.
