{"version": 3, "file": "crossPlatformSha256.js", "sourceRoot": "", "sources": ["../../src/crossPlatformSha256.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,MAAM,IAAI,eAAe,EAAE,MAAM,mBAAmB,CAAC;AAC9D,OAAO,EAAE,MAAM,IAAI,QAAQ,EAAE,MAAM,uBAAuB,CAAC;AAE3D,OAAO,EAAE,iBAAiB,EAAE,MAAM,iCAAiC,CAAC;AACpE,OAAO,EAAE,YAAY,EAAE,MAAM,6BAA6B,CAAC;AAC3D,OAAO,EAAE,eAAe,EAAE,MAAM,kBAAkB,CAAC;AAEnD;IAGE,gBAAY,MAAmB;QAC7B,IAAI,iBAAiB,CAAC,YAAY,EAAE,CAAC,EAAE;YACrC,IAAI,CAAC,IAAI,GAAG,IAAI,eAAe,CAAC,MAAM,CAAC,CAAC;SACzC;aAAM;YACL,IAAI,CAAC,IAAI,GAAG,IAAI,QAAQ,CAAC,MAAM,CAAC,CAAC;SAClC;IACH,CAAC;IAED,uBAAM,GAAN,UAAO,IAAgB,EAAE,QAAsC;QAC7D,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC,CAAC;IAC1C,CAAC;IAED,uBAAM,GAAN;QACE,OAAO,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;IAC5B,CAAC;IAED,sBAAK,GAAL;QACE,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;IACpB,CAAC;IACH,aAAC;AAAD,CAAC,AAtBD,IAsBC"}