import { WaiterConfiguration, WaiterResult } from "@smithy/util-waiter";
import { DescribeTableCommandInput } from "../commands/DescribeTableCommand";
import { DynamoDBClient } from "../DynamoDBClient";
export declare const waitForTableExists: (
  params: WaiterConfiguration<DynamoDBClient>,
  input: DescribeTableCommandInput
) => Promise<WaiterResult>;
export declare const waitUntilTableExists: (
  params: WaiterConfiguration<DynamoDBClient>,
  input: DescribeTableCommandInput
) => Promise<WaiterResult>;
