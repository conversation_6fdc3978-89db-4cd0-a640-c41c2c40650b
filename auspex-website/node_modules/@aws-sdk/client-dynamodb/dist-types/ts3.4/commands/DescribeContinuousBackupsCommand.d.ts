import { Command as $Command } from "@smithy/smithy-client";
import { Metada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import {
  DynamoDBClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../DynamoDBClient";
import {
  DescribeContinuousBackupsInput,
  DescribeContinuousBackupsOutput,
} from "../models/models_0";
export { __MetadataBearer };
export { $Command };
export interface DescribeContinuousBackupsCommandInput
  extends DescribeContinuousBackupsInput {}
export interface DescribeContinuousBackupsCommandOutput
  extends DescribeContinuousBackupsOutput,
    __MetadataBearer {}
declare const DescribeContinuousBackupsCommand_base: {
  new (
    input: DescribeContinuousBackupsCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DescribeContinuousBackupsCommandInput,
    DescribeContinuousBackupsCommandOutput,
    DynamoDBClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: DescribeContinuousBackupsCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DescribeContinuousBackupsCommandInput,
    DescribeContinuousBackupsCommandOutput,
    DynamoDBClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class DescribeContinuousBackupsCommand extends DescribeContinuousBackupsCommand_base {
  protected static __types: {
    api: {
      input: DescribeContinuousBackupsInput;
      output: DescribeContinuousBackupsOutput;
    };
    sdk: {
      input: DescribeContinuousBackupsCommandInput;
      output: DescribeContinuousBackupsCommandOutput;
    };
  };
}
