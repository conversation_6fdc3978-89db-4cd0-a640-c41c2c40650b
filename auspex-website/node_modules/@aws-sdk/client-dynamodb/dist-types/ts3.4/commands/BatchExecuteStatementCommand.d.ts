import { Command as $Command } from "@smithy/smithy-client";
import { Metada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import {
  DynamoDBClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../DynamoDBClient";
import {
  BatchExecuteStatementInput,
  BatchExecuteStatementOutput,
} from "../models/models_0";
export { __MetadataBearer };
export { $Command };
export interface BatchExecuteStatementCommandInput
  extends BatchExecuteStatementInput {}
export interface BatchExecuteStatementCommandOutput
  extends BatchExecuteStatementOutput,
    __MetadataBearer {}
declare const BatchExecuteStatementCommand_base: {
  new (
    input: BatchExecuteStatementCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    BatchExecuteStatementCommandInput,
    BatchExecuteStatementCommandOutput,
    DynamoDBClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: BatchExecuteStatementCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    BatchExecuteStatementCommandInput,
    BatchExecuteStatementCommandOutput,
    DynamoDBClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class BatchExecuteStatementCommand extends BatchExecuteStatementCommand_base {
  protected static __types: {
    api: {
      input: BatchExecuteStatementInput;
      output: BatchExecuteStatementOutput;
    };
    sdk: {
      input: BatchExecuteStatementCommandInput;
      output: BatchExecuteStatementCommandOutput;
    };
  };
}
