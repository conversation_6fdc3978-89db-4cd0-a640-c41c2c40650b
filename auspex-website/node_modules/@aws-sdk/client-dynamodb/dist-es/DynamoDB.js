import { createAggregatedClient } from "@smithy/smithy-client";
import { BatchExecuteStatementCommand, } from "./commands/BatchExecuteStatementCommand";
import { BatchGetItemCommand, } from "./commands/BatchGetItemCommand";
import { BatchWriteItemCommand, } from "./commands/BatchWriteItemCommand";
import { CreateBackupCommand, } from "./commands/CreateBackupCommand";
import { CreateGlobalTableCommand, } from "./commands/CreateGlobalTableCommand";
import { CreateTableCommand } from "./commands/CreateTableCommand";
import { DeleteBackupCommand, } from "./commands/DeleteBackupCommand";
import { DeleteItemCommand } from "./commands/DeleteItemCommand";
import { DeleteResourcePolicyCommand, } from "./commands/DeleteResourcePolicyCommand";
import { DeleteTableCommand } from "./commands/DeleteTableCommand";
import { DescribeBackupCommand, } from "./commands/DescribeBackupCommand";
import { DescribeContinuousBackupsCommand, } from "./commands/DescribeContinuousBackupsCommand";
import { DescribeContributorInsightsCommand, } from "./commands/DescribeContributorInsightsCommand";
import { DescribeEndpointsCommand, } from "./commands/DescribeEndpointsCommand";
import { DescribeExportCommand, } from "./commands/DescribeExportCommand";
import { DescribeGlobalTableCommand, } from "./commands/DescribeGlobalTableCommand";
import { DescribeGlobalTableSettingsCommand, } from "./commands/DescribeGlobalTableSettingsCommand";
import { DescribeImportCommand, } from "./commands/DescribeImportCommand";
import { DescribeKinesisStreamingDestinationCommand, } from "./commands/DescribeKinesisStreamingDestinationCommand";
import { DescribeLimitsCommand, } from "./commands/DescribeLimitsCommand";
import { DescribeTableCommand, } from "./commands/DescribeTableCommand";
import { DescribeTableReplicaAutoScalingCommand, } from "./commands/DescribeTableReplicaAutoScalingCommand";
import { DescribeTimeToLiveCommand, } from "./commands/DescribeTimeToLiveCommand";
import { DisableKinesisStreamingDestinationCommand, } from "./commands/DisableKinesisStreamingDestinationCommand";
import { EnableKinesisStreamingDestinationCommand, } from "./commands/EnableKinesisStreamingDestinationCommand";
import { ExecuteStatementCommand, } from "./commands/ExecuteStatementCommand";
import { ExecuteTransactionCommand, } from "./commands/ExecuteTransactionCommand";
import { ExportTableToPointInTimeCommand, } from "./commands/ExportTableToPointInTimeCommand";
import { GetItemCommand } from "./commands/GetItemCommand";
import { GetResourcePolicyCommand, } from "./commands/GetResourcePolicyCommand";
import { ImportTableCommand } from "./commands/ImportTableCommand";
import { ListBackupsCommand } from "./commands/ListBackupsCommand";
import { ListContributorInsightsCommand, } from "./commands/ListContributorInsightsCommand";
import { ListExportsCommand } from "./commands/ListExportsCommand";
import { ListGlobalTablesCommand, } from "./commands/ListGlobalTablesCommand";
import { ListImportsCommand } from "./commands/ListImportsCommand";
import { ListTablesCommand } from "./commands/ListTablesCommand";
import { ListTagsOfResourceCommand, } from "./commands/ListTagsOfResourceCommand";
import { PutItemCommand } from "./commands/PutItemCommand";
import { PutResourcePolicyCommand, } from "./commands/PutResourcePolicyCommand";
import { QueryCommand } from "./commands/QueryCommand";
import { RestoreTableFromBackupCommand, } from "./commands/RestoreTableFromBackupCommand";
import { RestoreTableToPointInTimeCommand, } from "./commands/RestoreTableToPointInTimeCommand";
import { ScanCommand } from "./commands/ScanCommand";
import { TagResourceCommand } from "./commands/TagResourceCommand";
import { TransactGetItemsCommand, } from "./commands/TransactGetItemsCommand";
import { TransactWriteItemsCommand, } from "./commands/TransactWriteItemsCommand";
import { UntagResourceCommand, } from "./commands/UntagResourceCommand";
import { UpdateContinuousBackupsCommand, } from "./commands/UpdateContinuousBackupsCommand";
import { UpdateContributorInsightsCommand, } from "./commands/UpdateContributorInsightsCommand";
import { UpdateGlobalTableCommand, } from "./commands/UpdateGlobalTableCommand";
import { UpdateGlobalTableSettingsCommand, } from "./commands/UpdateGlobalTableSettingsCommand";
import { UpdateItemCommand } from "./commands/UpdateItemCommand";
import { UpdateKinesisStreamingDestinationCommand, } from "./commands/UpdateKinesisStreamingDestinationCommand";
import { UpdateTableCommand } from "./commands/UpdateTableCommand";
import { UpdateTableReplicaAutoScalingCommand, } from "./commands/UpdateTableReplicaAutoScalingCommand";
import { UpdateTimeToLiveCommand, } from "./commands/UpdateTimeToLiveCommand";
import { DynamoDBClient } from "./DynamoDBClient";
const commands = {
    BatchExecuteStatementCommand,
    BatchGetItemCommand,
    BatchWriteItemCommand,
    CreateBackupCommand,
    CreateGlobalTableCommand,
    CreateTableCommand,
    DeleteBackupCommand,
    DeleteItemCommand,
    DeleteResourcePolicyCommand,
    DeleteTableCommand,
    DescribeBackupCommand,
    DescribeContinuousBackupsCommand,
    DescribeContributorInsightsCommand,
    DescribeEndpointsCommand,
    DescribeExportCommand,
    DescribeGlobalTableCommand,
    DescribeGlobalTableSettingsCommand,
    DescribeImportCommand,
    DescribeKinesisStreamingDestinationCommand,
    DescribeLimitsCommand,
    DescribeTableCommand,
    DescribeTableReplicaAutoScalingCommand,
    DescribeTimeToLiveCommand,
    DisableKinesisStreamingDestinationCommand,
    EnableKinesisStreamingDestinationCommand,
    ExecuteStatementCommand,
    ExecuteTransactionCommand,
    ExportTableToPointInTimeCommand,
    GetItemCommand,
    GetResourcePolicyCommand,
    ImportTableCommand,
    ListBackupsCommand,
    ListContributorInsightsCommand,
    ListExportsCommand,
    ListGlobalTablesCommand,
    ListImportsCommand,
    ListTablesCommand,
    ListTagsOfResourceCommand,
    PutItemCommand,
    PutResourcePolicyCommand,
    QueryCommand,
    RestoreTableFromBackupCommand,
    RestoreTableToPointInTimeCommand,
    ScanCommand,
    TagResourceCommand,
    TransactGetItemsCommand,
    TransactWriteItemsCommand,
    UntagResourceCommand,
    UpdateContinuousBackupsCommand,
    UpdateContributorInsightsCommand,
    UpdateGlobalTableCommand,
    UpdateGlobalTableSettingsCommand,
    UpdateItemCommand,
    UpdateKinesisStreamingDestinationCommand,
    UpdateTableCommand,
    UpdateTableReplicaAutoScalingCommand,
    UpdateTimeToLiveCommand,
};
export class DynamoDB extends DynamoDBClient {
}
createAggregatedClient(commands, DynamoDB);
